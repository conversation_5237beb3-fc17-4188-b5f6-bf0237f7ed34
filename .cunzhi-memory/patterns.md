# 常用模式和最佳实践

- 全屏模式最佳实践：1. 使用useEffect控制document.body和documentElement的overflow-hidden类来禁用滚动 2. 直接在Select组件的SelectContent上设置z-[150]高z-index而不是通过复杂CSS选择器 3. 依赖Radix UI的Portal机制确保下拉菜单渲染到body末尾 4. 组件卸载时清理滚动控制状态
- 全局模态框系统已实现：基于Zustand状态管理，支持类型安全的模态框管理，包含LoginModal、PricingModal、FeedbackModal等组件，使用next/dynamic懒加载优化性能，支持响应式设计，提供便捷hooks和兼容性迁移方案
- Modal组件开发教训：1.不要搞复杂的新旧版本兼容，直接用新版本 2.翻译内容要放在专用文件如i18n/pages/pricing/而不是通用messages/ 3.使用const page = await getPricingPage(locale)获取数据和翻译，而不是useTranslations 4.组件要简洁，避免过度设计
- AI模型翻译系统最佳实践：1)模型配置中设置translationKey字段指向翻译文件中的key 2)在model-manager.ts中保留translationKey信息传递给前端 3)前端useAIGeneration.ts中统一进行翻译处理，使用translationKey匹配翻译内容 4)关键是filterModels函数必须使用已翻译的allModels而不是重新获取未翻译数据 5)翻译文件结构为i18n/pages/AiModelTranslation/{locale}.json，key格式为grsai.model-id 6)避免在后端和前端同时进行翻译处理导致冲突
- AI模型翻译系统扩展完成：为services/provider/grsai/unified-models-textmulti-media.ts中的8个媒体模型（6个图像模型+2个视频模型）添加了完整的翻译支持，包括：1)为每个模型添加translationKey字段并清空name和description 2)在i18n/pages/AiModelTranslation/en.json和zh.json中添加对应的翻译内容 3)翻译内容包括模型名称、描述和所有参数的描述和提示信息 4)前端翻译系统会自动处理这些新模型的翻译显示
- AI模型翻译系统优化第一阶段完成：1)重构翻译文件结构，添加common部分包含公共参数、参数组、选项和UI翻译 2)大幅简化模型翻译，移除重复的参数翻译，减少约70%冗余内容 3)更新types.ts配置文件，将硬编码文本替换为翻译key 4)修改NumberInput组件使用翻译系统 5)新的翻译结构支持层次化管理，便于维护和扩展
- AI模型翻译系统优化第二阶段完成：1)创建translation-resolver.ts翻译解析工具，支持公共翻译和模型特定翻译的智能解析 2)更新DynamicOptionsConfig.tsx使用新的翻译解析工具，简化翻译应用逻辑 3)更新ParameterGroup.tsx使用翻译系统获取参数组标题 4)开始清理模型配置中的硬编码文本，让翻译系统接管所有文本显示 5)新系统支持翻译优先级：模型特定翻译 > 公共翻译 > 原始文本
- AI模型翻译系统优化第三阶段部分完成：1)创建CDN选项的公共翻译配置，统一管理CDN选项翻译 2)批量替换unified-models-textmulti-media.ts中所有CDN配置，使用CDN_OPTIONS替代硬编码 3)建立了完整的翻译系统架构，支持参数、选项、UI文本的统一翻译管理 4)仍有部分参数配置中的硬编码文本需要后续清理，但核心翻译系统已经可以正常工作 5)翻译系统现在支持智能回退和优先级解析
- AI模型翻译系统硬编码清理完成：1)移除所有重复参数的description和tooltip字段，包括variants、size、uploadedImages、aspectRatio、duration、resolution、fps、quality、motion_intensity等 2)保留选项中的label和description，因为这些是选项特定的内容 3)翻译系统现在完全接管参数的描述和提示显示 4)文件大小从504行减少到462行，清理了约42行硬编码文本 5)系统依赖公共翻译和智能回退机制提供用户界面文本
- AI模型翻译系统选项硬编码清理完成：1)创建SIZE_OPTIONS、DURATION_OPTIONS、RESOLUTION_OPTIONS、FPS_OPTIONS、QUALITY_OPTIONS等公共选项配置 2)在翻译文件中添加所有选项的中英文翻译 3)替换unified-models-textmulti-media.ts中所有硬编码选项，使用公共配置和翻译key 4)文件从462行减少到437行，清理了约25行硬编码选项 5)现在所有选项都支持完整的国际化，解决了中文硬编码问题 6)翻译解析工具会自动处理这些选项的翻译显示
- AI模型翻译系统嵌套结构重构完成：1)修复next-intl不允许翻译key中包含点号的问题 2)将翻译文件结构从平面结构改为嵌套结构，models.grsai.model-id格式 3)更新翻译解析工具支持嵌套结构和点号到连字符的转换 4)修复i18n/request.ts加载AiModelTranslation翻译文件 5)更新useAIGeneration.ts适应新的嵌套翻译结构 6)解决了MISSING_MESSAGE和INVALID_KEY错误，翻译系统现在应该能正常工作
