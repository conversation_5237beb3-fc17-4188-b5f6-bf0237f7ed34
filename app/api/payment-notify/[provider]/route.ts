import { PaymentFactory } from '@/services/payment/factory';
import { handlePaymentCallback } from '@/services/order';
import { respOk, respErr } from '@/lib/resp';

export async function POST(
  req: Request,
  { params }: { params: Promise<{ provider: string }> }
) {
  try {
    const { provider } = await params;

    // 验证支付平台
    const enabledProviders = PaymentFactory.getEnabledProviders();
    if (!enabledProviders.includes(provider)) {
      return respErr(`Unsupported payment provider: ${provider}`);
    }

    const body = await req.text();
    const signature = req.headers.get(`${provider}-signature`) ||
                     req.headers.get('stripe-signature') ||
                     req.headers.get('x-creem-signature') || '';

    if (!signature || !body) {
      return respErr("invalid notify data");
    }

    // 使用对应的支付提供商处理webhook
    const paymentProvider = PaymentFactory.createProvider(provider);
    const result = await paymentProvider.handleWebhook(body, signature);

    if (result.success && result.orderNo && result.sessionId) {
      // 调用通用的订单处理逻辑
      await handlePaymentCallback(provider, result.sessionId, result.orderNo);
    }

    return respOk();
  } catch (e: any) {
    console.log(`${provider} webhook failed:`, e);
    return respErr(`webhook failed: ${e.message}`);
  }
}
