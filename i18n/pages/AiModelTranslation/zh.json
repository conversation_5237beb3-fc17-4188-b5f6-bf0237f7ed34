{"common": {"parameters": {"max_tokens": {"description": "最大输出长度", "tooltip": "生成文本的最大token数量，影响回答长度"}, "temperature": {"description": "创造性程度", "tooltip": "控制输出的随机性，0为最保守，1为最创新"}, "top_p": {"description": "核心采样", "tooltip": "控制词汇选择范围，较小值使输出更集中"}, "stream": {"description": "流式输出", "tooltip": "启用后将实时显示生成过程"}, "aspectRatio": {"description": "图片宽高比", "tooltip": "选择生成图片的宽高比例"}, "cdn": {"description": "CDN选择", "tooltip": "选择存储和访问的CDN节点"}, "uploadedImages": {"description": "参考图片", "tooltip": "上传参考图片，支持多张图片"}, "variants": {"description": "生成数量", "tooltip": "选择生成图片的数量"}, "size": {"description": "图片尺寸", "tooltip": "选择生成图片的具体尺寸"}, "vision_detail": {"description": "图像分析细节", "tooltip": "控制图像分析的详细程度", "options": {"auto": {"label": "自动", "description": "自动选择最佳图像处理方式"}, "low": {"label": "低细节", "description": "快速处理，适合简单图像"}, "high": {"label": "高细节", "description": "详细分析，适合复杂图像"}}}, "duration": {"description": "视频时长", "tooltip": "选择生成视频的时长"}, "resolution": {"description": "视频分辨率", "tooltip": "选择生成视频的分辨率"}, "fps": {"description": "帧率", "tooltip": "选择视频的帧率"}, "quality": {"description": "生成质量", "tooltip": "选择生成的质量级别"}, "motion_intensity": {"description": "运动强度", "tooltip": "控制运动幅度，1为静态，10为剧烈运动"}}, "parameterGroups": {"basic": "基础设置", "advanced": "高级设置", "expert": "专家设置"}, "options": {"aspectRatio": {"1:1": {"label": "正方形 (1:1)", "description": "适合社交媒体头像"}, "16:9": {"label": "横屏 (16:9)", "description": "适合视频封面"}, "9:16": {"label": "竖屏 (9:16)", "description": "适合手机壁纸"}, "4:3": {"label": "标准 (4:3)", "description": "经典照片比例"}, "3:2": {"label": "照片 (3:2)", "description": "相机默认比例"}, "21:9": {"label": "超宽 (21:9)", "description": "电影级比例"}, "2:3": {"label": "竖版照片 (2:3)", "description": "竖版相机比例"}, "4:5": {"label": "Instagram (4:5)", "description": "Instagram竖版"}, "5:4": {"label": "经典横版 (5:4)", "description": "经典横版比例"}, "3:4": {"label": "标准竖版 (3:4)", "description": "标准竖版比例"}}, "cdn": {"global": {"label": "全球CDN", "description": "使用全球CDN加速"}, "zh": {"label": "中国CDN", "description": "使用中国CDN加速"}}, "size": {"1024x1024": {"label": "正方形 (1024x1024)", "description": "标准正方形尺寸"}, "1792x1024": {"label": "横屏 (1792x1024)", "description": "宽屏尺寸"}, "1024x1792": {"label": "竖屏 (1024x1792)", "description": "竖屏尺寸"}}, "duration": {"5": {"label": "5秒", "description": "短视频，快速生成"}, "10": {"label": "10秒", "description": "中等长度视频"}, "15": {"label": "15秒", "description": "长视频，更丰富内容"}}, "resolution": {"720p": {"label": "720p (HD)", "description": "高清分辨率，平衡质量和速度"}, "1080p": {"label": "1080p (Full HD)", "description": "全高清分辨率，更高质量"}, "4k": {"label": "4K (Ultra HD)", "description": "超高清分辨率，最高质量"}}, "fps": {"24": {"label": "24 FPS", "description": "电影级帧率"}, "30": {"label": "30 FPS", "description": "标准视频帧率"}, "60": {"label": "60 FPS", "description": "高帧率，更流畅"}}, "quality": {"standard": {"label": "标准质量", "description": "平衡质量和速度"}, "high": {"label": "高质量", "description": "更好的视觉效果"}, "ultra": {"label": "超高质量", "description": "最佳视觉效果，生成较慢"}}}, "ui": {"range": "范围", "minValue": "最小值", "maxValue": "最大值", "step": "步长"}}, "models": {"grsai": {"gemini-2-5-pro": {"name": "Gemini 2.5 Pro", "description": "高级对话模型，适合复杂任务和专业用途"}, "gemini-2-5-flash": {"name": "Gemini 2.5 Flash", "description": "快速对话模型，响应迅速，效率高"}, "gemini-2-5-flash-lite": {"name": "Gemini 2.5 Flash Lite", "description": "轻量级对话模型，成本低廉，功能基础"}, "gpt-4o-mini": {"name": "GPT-4o Mini", "description": "GPT-4o 轻量版本，性能与成本平衡"}, "o4-mini-all": {"name": "GPT-4o Mini All", "description": "GPT-4o Mini 全功能版本，支持视觉等多模态能力", "parameters": {"uploadedImages": {"description": "图片输入", "tooltip": "上传图片进行视觉分析和理解"}}}, "gpt-4o-all": {"name": "GPT-4o All", "description": "GPT-4o 完整版本，具备所有高级功能和多模态支持", "parameters": {"uploadedImages": {"description": "图片输入", "tooltip": "上传图片进行视觉分析和理解"}}}, "flux-pro-1-1": {"name": "Flux Pro 1.1", "description": "Flux 技术 v1.1 专业图像生成"}, "gpt-4o-image": {"name": "GPT-4o Image", "description": "使用 GPT-4o 架构的高质量图像生成"}, "flux-kontext-pro": {"name": "Flux Kontext Pro", "description": "上下文感知的专业级图像生成"}, "sora-image": {"name": "Sora Image", "description": "基于 Sora 技术的先进图像生成模型"}, "flux-pro-1-1-ultra": {"name": "Flux Pro 1.1 Ultra", "description": "增强版 Flux Pro 超高质量图像生成"}, "flux-kontext-max": {"name": "Flux Kontext Max", "description": "最高质量的上下文感知图像生成"}, "veo3-fast": {"name": "Veo3 Fast", "description": "Veo3 技术快速视频生成，结果迅速", "parameters": {"uploadedImages": {"description": "首帧图片", "tooltip": "上传首帧图片作为视频的起始画面"}}}, "veo3-pro": {"name": "Veo3 Pro", "description": "具备高级 Veo3 能力的专业视频生成", "parameters": {"uploadedImages": {"description": "首帧图片", "tooltip": "上传首帧图片作为视频的起始画面"}}}}}}