import {
  CreditsTransType,
  increaseCredits,
  updateCreditForOrder,
} from "./credit";
import { findOrderByOrderNo, updateOrderStatus, findOrderByPaymentSession } from "@/models/order";
import { getIsoTimestr, getOneYearLaterTimestr } from "@/lib/time";

import <PERSON><PERSON> from "stripe";
import { updateAffiliateForOrder } from "./affiliate";
import { PaymentFactory } from "./payment/factory";
import { Order } from "@/types/order";

export async function handleOrderSession(session: Stripe.Checkout.Session) {
  try {
    if (
      !session ||
      !session.metadata ||
      !session.metadata.order_no ||
      session.payment_status !== "paid"
    ) {
      throw new Error("invalid session");
    }

    const order_no = session.metadata.order_no;
    const paid_email =
      session.customer_details?.email || session.customer_email || "";
    const paid_detail = JSON.stringify(session);

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== "created") {
      throw new Error("invalid order");
    }

    const paid_at = getIsoTimestr();
    await updateOrderStatus(order_no, "paid", paid_at, paid_email, paid_detail);

    if (order.user_uuid) {
      if (order.credits > 0) {
        // increase credits for paied order
        await updateCreditForOrder(order);
      }

      // update affiliate for paied order
      await updateAffiliateForOrder(order);
    }

    console.log(
      "handle order session successed: ",
      order_no,
      paid_at,
      paid_email,
      paid_detail
    );
  } catch (e) {
    console.log("handle order session failed: ", e);
    throw e;
  }
}

export async function handlePaymentCallback(
  provider: string,
  sessionId: string,
  orderNo?: string
) {
  try {
    // 获取支付会话详情
    const paymentProvider = PaymentFactory.createProvider(provider);
    const sessionData = await paymentProvider.retrieveSession(sessionId);

    // 查找订单
    let order: Order | undefined;
    if (orderNo) {
      order = await findOrderByOrderNo(orderNo);
    } else {
      order = await findOrderByPaymentSession(sessionId, provider);
    }

    if (!order || order.status !== "created") {
      throw new Error("Invalid order or order already processed");
    }

    // 验证支付状态
    if (sessionData.paymentStatus !== 'paid' && sessionData.paymentStatus !== 'complete') {
      throw new Error("Payment not completed");
    }

    // 更新订单状态
    const paid_at = getIsoTimestr();
    const paid_email = sessionData.customerEmail || order.user_email;
    const paid_detail = JSON.stringify(sessionData);

    await updateOrderStatus(order.order_no, "paid", paid_at, paid_email, paid_detail);

    // 发放积分和处理分销
    if (order.credits > 0) {
      await updateCreditForOrder(order);
    }
    await updateAffiliateForOrder(order);

    console.log(`Order ${order.order_no} processed successfully via ${provider}`);
  } catch (error) {
    console.error(`Failed to process ${provider} payment callback:`, error);
    throw error;
  }
}
