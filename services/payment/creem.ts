// services/payment/creem.ts
import { PaymentProvider, CheckoutParams, PaymentSession, WebhookResult, SessionData } from './types';

export class CreemProvider implements PaymentProvider {
  private apiKey: string;
  private baseUrl = 'https://api.creem.io/v1';
  
  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  validateConfig(): boolean {
    return !!this.apiKey;
  }

  async createCheckoutSession(params: CheckoutParams): Promise<PaymentSession> {
    const requestBody = {
      line_items: [{
        price_data: {
          currency: params.currency,
          unit_amount: params.amount,
          product_data: {
            name: params.productName
          }
        },
        quantity: 1
      }],
      mode: params.isSubscription ? 'subscription' : 'payment',
      success_url: params.successUrl,
      cancel_url: params.cancelUrl,
      customer_email: params.customerEmail,
      metadata: params.metadata
    };

    const response = await fetch(`${this.baseUrl}/checkout`, {
      method: 'POST',
      headers: {
        'x-api-key': this.apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Creem API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      id: data.id,
      url: data.url,
      provider: 'creem',
      metadata: { creemSessionId: data.id }
    };
  }

  async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
    // Creem webhook验证逻辑
    try {
      const event = JSON.parse(body);
      
      // 验证签名（根据Creem文档实现）
      if (!this.verifyWebhookSignature(body, signature)) {
        return { success: false, error: 'Invalid signature' };
      }

      if (event.type === 'checkout.session.completed') {
        return {
          success: true,
          orderNo: event.data.metadata?.order_no,
          sessionId: event.data.id
        };
      }

      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async retrieveSession(sessionId: string): Promise<SessionData> {
    const response = await fetch(`${this.baseUrl}/checkout/${sessionId}`, {
      headers: {
        'x-api-key': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to retrieve Creem session: ${response.statusText}`);
    }

    const session = await response.json();
    return {
      id: session.id,
      paymentStatus: session.status,
      customerEmail: session.customer_email,
      metadata: session.metadata || {}
    };
  }

  private verifyWebhookSignature(body: string, signature: string): boolean {
    // 实现Creem的webhook签名验证
    // 具体实现需要参考Creem文档
    return true; // 临时返回true，需要实际实现
  }
}
