// services/payment/factory.ts
import { PaymentProvider } from './types';
import { StripeProvider } from './stripe';
import { CreemProvider } from './creem';

export class PaymentFactory {
  private static providers: Map<string, PaymentProvider> = new Map();

  static createProvider(provider: string): PaymentProvider {
    if (this.providers.has(provider)) {
      return this.providers.get(provider)!;
    }

    let providerInstance: PaymentProvider;
    
    switch (provider) {
      case 'stripe':
        providerInstance = new StripeProvider(process.env.STRIPE_PRIVATE_KEY!);
        break;
      case 'creem':
        providerInstance = new CreemProvider(process.env.CREEM_API_KEY!);
        break;
      default:
        throw new Error(`Unsupported payment provider: ${provider}`);
    }

    // 验证配置
    if (!providerInstance.validateConfig()) {
      throw new Error(`Invalid configuration for provider: ${provider}`);
    }

    this.providers.set(provider, providerInstance);
    return providerInstance;
  }

  static getEnabledProviders(): string[] {
    return process.env.PAYMENT_PROVIDERS_ENABLED?.split(',') || ['stripe'];
  }

  static getDefaultProvider(): string {
    return process.env.PAYMENT_DEFAULT_PROVIDER || 'stripe';
  }
}
