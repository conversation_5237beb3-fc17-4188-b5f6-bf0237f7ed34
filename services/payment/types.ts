// services/payment/types.ts
export interface PaymentProvider {
  createCheckoutSession(params: CheckoutParams): Promise<PaymentSession>;
  handleWebhook(data: any, signature: string): Promise<WebhookResult>;
  retrieveSession(sessionId: string): Promise<SessionData>;
  validateConfig(): boolean;
}

export interface CheckoutParams {
  amount: number;
  currency: string;
  productName: string;
  productId: string;
  customerEmail?: string;
  successUrl: string;
  cancelUrl: string;
  metadata: Record<string, string>;
  isSubscription?: boolean;
  interval?: string;
}

export interface PaymentSession {
  id: string;
  url: string;
  provider: string;
  metadata?: Record<string, any>;
}

export interface WebhookResult {
  success: boolean;
  orderNo?: string;
  sessionId?: string;
  error?: string;
}

export interface SessionData {
  id: string;
  paymentStatus: string;
  customerEmail?: string;
  metadata: Record<string, any>;
}
