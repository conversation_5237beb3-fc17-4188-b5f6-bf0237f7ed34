/**
 * Replicate 统一配置导出
 * 合并模型配置和参数配置
 */

import { REPLICATE_UNIFIED_IMAGE_MODELS, UnifiedModelConfig, ModelType, Provider, UnitType } from './unified-models-image';
import { ModelParameterConfig } from '../types';

// 重新导出类型
export type { UnifiedModelConfig };
export { ModelType, Provider, UnitType };

/**
 * 所有 Replicate 统一模型配置
 */
export const ALL_REPLICATE_UNIFIED_MODELS = [
  ...REPLICATE_UNIFIED_IMAGE_MODELS
];

/**
 * 转换为参数管理器兼容的格式
 */
export const ALL_REPLICATE_PARAMETER_CONFIGS: ModelParameterConfig[] = ALL_REPLICATE_UNIFIED_MODELS.map(model => ({
  modelId: model.id,
  version: '1.0',
  provider: model.provider.toLowerCase(),
  modelType: model.type,
  parameters: model.parameters,
  parameterGroups: model.parameterGroups
}));

/**
 * 根据模型ID获取统一配置
 */
export function getUnifiedModelConfig(modelId: string) {
  return ALL_REPLICATE_UNIFIED_MODELS.find(model => model.id === modelId);
}

/**
 * 根据模型类型获取统一配置
 */
export function getUnifiedModelsByType(type: string) {
  return ALL_REPLICATE_UNIFIED_MODELS.filter(model => model.type === type && model.isActive);
}

/**
 * 获取所有活跃的统一模型
 */
export function getAllActiveUnifiedModels() {
  return ALL_REPLICATE_UNIFIED_MODELS.filter(model => model.isActive);
}

/**
 * 根据提供商获取统一配置
 */
export function getUnifiedModelsByProvider(provider: string) {
  return ALL_REPLICATE_UNIFIED_MODELS.filter(model => 
    model.provider.toLowerCase() === provider.toLowerCase() && model.isActive
  );
}

/**
 * 获取模型支持的功能
 */
export function getModelSupportedFeatures(modelId: string): string[] {
  const model = getUnifiedModelConfig(modelId);
  return model?.supportedFeatures || [];
}

/**
 * 检查模型是否支持特定功能
 */
export function isModelFeatureSupported(modelId: string, feature: string): boolean {
  const supportedFeatures = getModelSupportedFeatures(modelId);
  return supportedFeatures.includes(feature);
}

/**
 * 获取模型的成本信息
 */
export function getModelCostInfo(modelId: string) {
  const model = getUnifiedModelConfig(modelId);
  if (!model) return null;
  
  return {
    creditsPerUnit: model.creditsPerUnit,
    unitType: model.unitType,
    provider: model.provider
  };
}

/**
 * 获取模型的基础信息
 */
export function getModelBasicInfo(modelId: string) {
  const model = getUnifiedModelConfig(modelId);
  if (!model) return null;
  
  return {
    id: model.id,
    name: model.name,
    type: model.type,
    provider: model.provider,
    description: model.description,
    icon: model.icon,
    translationKey: model.translationKey
  };
}
