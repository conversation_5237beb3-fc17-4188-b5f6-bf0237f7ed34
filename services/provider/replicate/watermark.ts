import Jimp from 'jimp';

/**
 * 水印位置枚举
 */
export enum WatermarkPosition {
  TOP_LEFT = 'top-left',
  TOP_RIGHT = 'top-right',
  BOTTOM_LEFT = 'bottom-left',
  BOTTOM_RIGHT = 'bottom-right',
  CENTER = 'center'
}

/**
 * 水印配置接口
 */
export interface WatermarkConfig {
  text: string;
  position: WatermarkPosition;
  opacity: number; // 0-1 之间的透明度值
  fontSize?: number; // 字体大小，默认为图片宽度的 1/20
  color?: string; // 文字颜色，默认白色
  margin?: number; // 边距，默认为 20px
}

/**
 * 水印服务类
 */
export class WatermarkService {
  /**
   * 为图片添加水印
   * @param imageBuffer 原始图片的 Buffer
   * @param config 水印配置
   * @returns 添加水印后的图片 Buffer
   */
  static async addWatermark(
    imageBuffer: Buffer,
    config: WatermarkConfig
  ): Promise<Buffer> {
    try {
      console.log(`[Watermark Service] Starting watermark process`);
      console.log(`[Watermark Service] Config:`, config);

      // 读取图片
      const image = await Jimp.read(imageBuffer);
      const { width, height } = image.bitmap;

      console.log(`[Watermark Service] Image dimensions: ${width}x${height}`);

      // 设置默认值
      const fontSize = config.fontSize || Math.floor(width / 20);
      const color = config.color || '#FFFFFF';
      const margin = config.margin || 20;
      const opacity = Math.max(0, Math.min(1, config.opacity)); // 确保在 0-1 范围内

      console.log(`[Watermark Service] Font size: ${fontSize}, Color: ${color}, Margin: ${margin}, Opacity: ${opacity}`);

      // 加载字体
      const font = await Jimp.loadFont(Jimp.FONT_SANS_32_WHITE);

      // 计算文字尺寸
      const textWidth = Jimp.measureText(font, config.text);
      const textHeight = Jimp.measureTextHeight(font, config.text, textWidth);

      console.log(`[Watermark Service] Text dimensions: ${textWidth}x${textHeight}`);

      // 计算水印位置
      const position = this.calculatePosition(
        config.position,
        width,
        height,
        textWidth,
        textHeight,
        margin
      );

      console.log(`[Watermark Service] Watermark position: x=${position.x}, y=${position.y}`);

      // 创建文字图层
      const textImage = new Jimp(textWidth, textHeight, 0x00000000); // 透明背景
      textImage.print(font, 0, 0, config.text);

      // 应用透明度
      textImage.opacity(opacity);

      // 将文字图层合成到原图上
      image.composite(textImage, position.x, position.y);

      console.log(`[Watermark Service] Watermark applied successfully`);

      // 返回处理后的图片 Buffer
      return await image.getBufferAsync(Jimp.MIME_PNG);
    } catch (error) {
      console.error(`[Watermark Service] Error adding watermark:`, error);
      throw new Error(`Failed to add watermark: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 计算水印位置
   */
  private static calculatePosition(
    position: WatermarkPosition,
    imageWidth: number,
    imageHeight: number,
    textWidth: number,
    textHeight: number,
    margin: number
  ): { x: number; y: number } {
    switch (position) {
      case WatermarkPosition.TOP_LEFT:
        return { x: margin, y: margin };

      case WatermarkPosition.TOP_RIGHT:
        return { x: imageWidth - textWidth - margin, y: margin };

      case WatermarkPosition.BOTTOM_LEFT:
        return { x: margin, y: imageHeight - textHeight - margin };

      case WatermarkPosition.BOTTOM_RIGHT:
        return { x: imageWidth - textWidth - margin, y: imageHeight - textHeight - margin };

      case WatermarkPosition.CENTER:
        return {
          x: Math.floor((imageWidth - textWidth) / 2),
          y: Math.floor((imageHeight - textHeight) / 2)
        };

      default:
        return { x: margin, y: imageHeight - textHeight - margin }; // 默认左下角
    }
  }

  /**
   * 从 URL 下载图片并添加水印
   * @param imageUrl 图片 URL
   * @param config 水印配置
   * @returns 添加水印后的图片 Buffer
   */
  static async addWatermarkFromUrl(
    imageUrl: string,
    config: WatermarkConfig
  ): Promise<Buffer> {
    try {
      console.log(`[Watermark Service] Downloading image from URL: ${imageUrl}`);

      // 下载图片
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
      }

      const imageBuffer = Buffer.from(await response.arrayBuffer());
      console.log(`[Watermark Service] Downloaded image, size: ${imageBuffer.length} bytes`);

      // 添加水印
      return await this.addWatermark(imageBuffer, config);
    } catch (error) {
      console.error(`[Watermark Service] Error downloading and watermarking image:`, error);
      throw error;
    }
  }

  /**
   * 获取默认水印配置
   */
  static getDefaultConfig(): WatermarkConfig {
    const webUrl = process.env.NEXT_PUBLIC_WEB_URL || 'kreaflux.org';

    return {
      text: webUrl,
      position: WatermarkPosition.BOTTOM_RIGHT,
      opacity: 0.7,
      fontSize: undefined, // 使用自动计算
      color: '#FFFFFF',
      margin: 20
    };
  }
}